<?php

namespace Webkul\Admin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AdminLocale
{
    /**
     * 支持的后台语言列表
     */
    protected $supportedLocales = [
        'en' => 'English',
        'zh_CN' => '中文',
        'it' => 'Italiano',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取session中存储的后台语言设置
        $adminLocale = session('admin_locale');
        
        // 如果没有设置或设置的语言不在支持列表中，使用默认英语
        if (!$adminLocale || !array_key_exists($adminLocale, $this->supportedLocales)) {
            $adminLocale = 'en';
        }
        
        // 只为当前请求设置语言，不改变系统默认语言
        app()->setLocale($adminLocale);
        
        // 将当前后台语言信息存储到视图中，供前端使用
        view()->share('currentAdminLocale', $adminLocale);
        view()->share('supportedAdminLocales', $this->supportedLocales);
        
        return $next($request);
    }
    
    /**
     * 获取支持的语言列表
     *
     * @return array
     */
    public function getSupportedLocales()
    {
        return $this->supportedLocales;
    }
}
