<?php

namespace Webkul\Admin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AdminLocale
{
    /**
     * 支持的后台语言列表
     */
    protected $supportedLocales = [
        'en' => 'English',
        'zh_CN' => '中文',
        'it' => 'Italiano',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取session中存储的后台语言设置
        $adminLocale = session('admin_locale');

        // 如果没有设置或设置的语言不在支持列表中，使用默认英语
        if (!$adminLocale || !array_key_exists($adminLocale, $this->supportedLocales)) {
            $adminLocale = 'en';
        }

        // 将当前后台语言信息存储到视图中，供前端使用
        view()->share('currentAdminLocale', $adminLocale);
        view()->share('supportedAdminLocales', $this->supportedLocales);

        // 注册全局辅助函数用于Admin翻译
        $this->registerAdminTransHelpers();

        // 处理请求
        $response = $next($request);

        return $response;
    }

    /**
     * 注册Admin翻译辅助函数
     *
     * @return void
     */
    protected function registerAdminTransHelpers()
    {
        if (!function_exists('admin_trans')) {
            function admin_trans($key = null, $replace = [], $locale = null) {
                $adminLocale = session('admin_locale', 'en');

                if (is_null($key)) {
                    return app('translator');
                }

                // 临时设置语言进行翻译
                $originalLocale = app()->getLocale();
                app()->setLocale($locale ?: $adminLocale);

                $translation = trans($key, $replace);

                // 恢复原始语言设置
                app()->setLocale($originalLocale);

                return $translation;
            }
        }

        if (!function_exists('admin__')) {
            function admin__($key = null, $replace = [], $locale = null) {
                return admin_trans($key, $replace, $locale);
            }
        }
    }

    /**
     * 获取支持的语言列表
     *
     * @return array
     */
    public function getSupportedLocales()
    {
        return $this->supportedLocales;
    }
}
