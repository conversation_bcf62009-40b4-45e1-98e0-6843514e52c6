{"__meta": {"id": "01K1AD1HMS53GGD0789YP6F24Q", "datetime": "2025-07-29 07:13:45", "utime": **********.241445, "method": "GET", "uri": "/admin/dashboard/stats?type=over-all", "ip": "127.0.0.1"}, "modules": {"count": 4, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `customers` where `channel_id` in (1) and `created_at` between '2025-05-30 00:00:00' and '2025-06-29 00:00:00'", "duration": 2.91, "duration_str": "2.91s", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `customers` where `channel_id` in (1) and `created_at` between '2025-06-29 00:00:00' and '2025-07-29 07:13:45'", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}]}, {"name": "Webkul\\Sales", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-05-30 00:00:00' and '2025-06-29 00:00:00'", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-29 00:00:00' and '2025-07-29 07:13:45'", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-05-30 00:00:00' and '2025-06-29 00:00:00'", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-29 00:00:00' and '2025-07-29 07:13:45'", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select avg(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-05-30 00:00:00' and '2025-06-29 00:00:00'", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select avg(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-29 00:00:00' and '2025-07-29 07:13:45'", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select sum(`grand_total`) as aggregate from `invoices` where `state` = 'pending'", "duration": 3.32, "duration_str": "3.32s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.35, "duration_str": "350ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753769624.939592, "end": **********.248967, "duration": 0.3093750476837158, "duration_str": "309ms", "measures": [{"label": "Booting", "start": 1753769624.939592, "relative_start": 0, "end": **********.145107, "relative_end": **********.145107, "duration": 0.*****************, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.145118, "relative_start": 0.*****************, "end": **********.248971, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.157077, "relative_start": 0.****************, "end": **********.160164, "relative_end": **********.160164, "duration": 0.0030870437622070312, "duration_str": "3.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.239894, "relative_start": 0.*****************, "end": **********.240177, "relative_end": **********.240177, "duration": 0.0002830028533935547, "duration_str": "283μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 16, "nb_statements": 16, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.010260000000000002, "accumulated_duration_str": "10.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.1913712, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 4.386}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.195105, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 4.386, "width_percent": 1.852}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.199095, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 6.238, "width_percent": 2.047}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.200735, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 8.285, "width_percent": 2.827}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.2020829, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 11.111, "width_percent": 1.852}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.207257, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 12.963, "width_percent": 3.411}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.210598, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 16.374, "width_percent": 2.924}, {"sql": "select count(*) as aggregate from `customers` where `channel_id` in (1) and `created_at` between '2025-05-30 00:00:00' and '2025-06-29 00:00:00'", "type": "query", "params": [], "bindings": [1, "2025-05-30 00:00:00", "2025-06-29 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 84}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 32}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 33}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2129061, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "Customer.php:84", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FCustomer.php&line=84", "ajax": false, "filename": "Customer.php", "line": "84"}, "connection": "mlk", "explain": null, "start_percent": 19.298, "width_percent": 28.363}, {"sql": "select count(*) as aggregate from `customers` where `channel_id` in (1) and `created_at` between '2025-06-29 00:00:00' and '2025-07-29 07:13:45'", "type": "query", "params": [], "bindings": [1, "2025-06-29 00:00:00", "2025-07-29 07:13:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 84}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 33}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 33}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.217216, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Customer.php:84", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FCustomer.php&line=84", "ajax": false, "filename": "Customer.php", "line": "84"}, "connection": "mlk", "explain": null, "start_percent": 47.661, "width_percent": 2.924}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-05-30 00:00:00' and '2025-06-29 00:00:00'", "type": "query", "params": [], "bindings": [1, "2025-05-30 00:00:00", "2025-06-29 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 36}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 34}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.219048, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Sale.php:76", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=76", "ajax": false, "filename": "Sale.php", "line": "76"}, "connection": "mlk", "explain": null, "start_percent": 50.585, "width_percent": 2.729}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-29 00:00:00' and '2025-07-29 07:13:45'", "type": "query", "params": [], "bindings": [1, "2025-06-29 00:00:00", "2025-07-29 07:13:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 37}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 34}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.220711, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Sale.php:76", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=76", "ajax": false, "filename": "Sale.php", "line": "76"}, "connection": "mlk", "explain": null, "start_percent": 53.314, "width_percent": 2.437}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-05-30 00:00:00' and '2025-06-29 00:00:00'", "type": "query", "params": [], "bindings": [1, "2025-05-30 00:00:00", "2025-06-29 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 130}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 35}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2227309, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Sale.php:175", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=175", "ajax": false, "filename": "Sale.php", "line": "175"}, "connection": "mlk", "explain": null, "start_percent": 55.75, "width_percent": 3.509}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-29 00:00:00' and '2025-07-29 07:13:45'", "type": "query", "params": [], "bindings": [1, "2025-06-29 00:00:00", "2025-07-29 07:13:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 131}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 35}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.224257, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Sale.php:175", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=175", "ajax": false, "filename": "Sale.php", "line": "175"}, "connection": "mlk", "explain": null, "start_percent": 59.259, "width_percent": 2.632}, {"sql": "select avg(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-05-30 00:00:00' and '2025-06-29 00:00:00'", "type": "query", "params": [], "bindings": [1, "2025-05-30 00:00:00", "2025-06-29 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 259}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 239}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 36}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.227889, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Sale.php:259", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=259", "ajax": false, "filename": "Sale.php", "line": "259"}, "connection": "mlk", "explain": null, "start_percent": 61.891, "width_percent": 3.509}, {"sql": "select avg(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-29 00:00:00' and '2025-07-29 07:13:45'", "type": "query", "params": [], "bindings": [1, "2025-06-29 00:00:00", "2025-07-29 07:13:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 259}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 240}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 36}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.229621, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Sale.php:259", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=259", "ajax": false, "filename": "Sale.php", "line": "259"}, "connection": "mlk", "explain": null, "start_percent": 65.4, "width_percent": 2.242}, {"sql": "select sum(`grand_total`) as aggregate from `invoices` where `state` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/InvoiceRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Sales\\src\\Repositories\\InvoiceRepository.php", "line": 333}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 578}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 38}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2311351, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "InvoiceRepository.php:333", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/InvoiceRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Sales\\src\\Repositories\\InvoiceRepository.php", "line": 333}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FInvoiceRepository.php&line=333", "ajax": false, "filename": "InvoiceRepository.php", "line": "333"}, "connection": "mlk", "explain": null, "start_percent": 67.641, "width_percent": 32.359}]}, "models": {"data": {"Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/admin/dashboard/stats?type=over-all", "action_name": "admin.dashboard.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats", "uri": "GET admin/dashboard/stats", "controller": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=49\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/dashboard", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=49\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/DashboardController.php:49-57</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, admin.locale, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "312ms", "peak_memory": "40MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-578224056 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">over-all</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578224056\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-342862456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-342862456\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-459250916 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">dark_mode=0; sidebar_collapsed=0; XSRF-TOKEN=eyJpdiI6ImVqWU5LWG9UUDUyZngrVzBqN2Fqd0E9PSIsInZhbHVlIjoiN01WSnBMakVJT3JpT3BMaEtvM2p1WVN1VE5WK2JuMmtVcWYvQ3JIN2JqNnFndmtuSWwrUnFZOHBiVFlXalR6RDl5NkY2OWxFamhWSmRWV254Wndlc1gzSzJOaU92LzVBR1ZhUlZJWFRMTVBLR3F1SkM1aXI4bzdDZFBBTE5TN2wiLCJtYWMiOiI5NDcxOTRmNTUzMGUxNzg5ZjcyZTBkMTlkMjA3MzY1YjA5MGQ2NzFmZTQ3MmY1NDJmMDZjMzdiNGM1MDlhNzYwIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IklvRG0vY3QwTXFrSFFnVWNZVVBhcmc9PSIsInZhbHVlIjoiaDB2SHdUekQreTZURUE1ZGhQYUdDZWduRDBGWU1SZitlZ25jT3l1NW5IWnA2MVdYbC9oWjJZbytFOTBudkhUYXhNLzRkSFdzeldQZkZnRWxuLzZwWVhLaXNXRklYL3FOY1AxR1pnVTlYZm4xdHBaS3Q1blRINzdYWUdKWDNvQ3kiLCJtYWMiOiJmYTA4YjZlOWMwYzlmOGIwNGQ3ZjBiOGNlNTJkNTRjZmQzNmM4YTk4ZDMzNTJmOTBjYmM0MzgyNWY2YjQxNmM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://mlk.test/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVqWU5LWG9UUDUyZngrVzBqN2Fqd0E9PSIsInZhbHVlIjoiN01WSnBMakVJT3JpT3BMaEtvM2p1WVN1VE5WK2JuMmtVcWYvQ3JIN2JqNnFndmtuSWwrUnFZOHBiVFlXalR6RDl5NkY2OWxFamhWSmRWV254Wndlc1gzSzJOaU92LzVBR1ZhUlZJWFRMTVBLR3F1SkM1aXI4bzdDZFBBTE5TN2wiLCJtYWMiOiI5NDcxOTRmNTUzMGUxNzg5ZjcyZTBkMTlkMjA3MzY1YjA5MGQ2NzFmZTQ3MmY1NDJmMDZjMzdiNGM1MDlhNzYwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459250916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1519435963 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OIqrKtEmbsI8ceyhgXM3fk5WEauxQf3GFdYor0uC</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NbBYhkEddkxEtXcLOZvh2s0lb1IV8SSuPtucxwG5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519435963\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-334524329 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:13:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334524329\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-998148138 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OIqrKtEmbsI8ceyhgXM3fk5WEauxQf3GFdYor0uC</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://mlk.test/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998148138\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/admin/dashboard/stats?type=over-all", "action_name": "admin.dashboard.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats"}, "badge": null}}