<?php

namespace Webkul\Admin\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AdminLocaleController extends Controller
{
    /**
     * 支持的后台语言列表
     */
    protected $supportedLocales = [
        'en' => 'English',
        'zh_CN' => '中文',
        'it' => 'Italiano',
    ];

    /**
     * 切换后台语言
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function switch(Request $request)
    {
        $locale = $request->input('locale');
        
        // 验证语言代码是否在支持列表中
        if (!array_key_exists($locale, $this->supportedLocales)) {
            if ($request->expectsJson()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Unsupported locale: ' . $locale
                ], 400);
            }
            
            return redirect()->back()->with('error', 'Unsupported locale: ' . $locale);
        }
        
        // 将语言设置存储到session中
        session(['admin_locale' => $locale]);
        
        if ($request->expectsJson()) {
            return new JsonResponse([
                'success' => true,
                'message' => 'Language switched successfully',
                'locale' => $locale,
                'locale_name' => $this->supportedLocales[$locale]
            ]);
        }
        
        return redirect()->back()->with('success', 'Language switched to ' . $this->supportedLocales[$locale]);
    }
    
    /**
     * 获取当前后台语言
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function current()
    {
        $currentLocale = session('admin_locale', 'en');

        return new JsonResponse([
            'current_locale' => $currentLocale,
            'current_locale_name' => $this->supportedLocales[$currentLocale] ?? 'English',
            'supported_locales' => $this->supportedLocales
        ]);
    }

    /**
     * 测试语言切换功能
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function test()
    {
        $currentLocale = session('admin_locale', 'en');
        $systemLocale = app()->getLocale();

        // 测试admin_trans函数
        $adminTransTest = function_exists('admin_trans') ? admin_trans('admin::app.components.layouts.header.language-switcher') : 'admin_trans not available';

        // 测试系统翻译
        $systemTransTest = trans('admin::app.components.layouts.header.language-switcher');

        return new JsonResponse([
            'admin_locale' => $currentLocale,
            'system_locale' => $systemLocale,
            'admin_trans_result' => $adminTransTest,
            'system_trans_result' => $systemTransTest,
            'functions_exist' => [
                'admin_trans' => function_exists('admin_trans'),
                'admin__' => function_exists('admin__'),
            ]
        ]);
    }
}
