<?php

namespace Webkul\Admin\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AdminLocaleController extends Controller
{
    /**
     * 支持的后台语言列表
     */
    protected $supportedLocales = [
        'en' => 'English',
        'zh_CN' => '中文',
        'it' => 'Italiano',
    ];

    /**
     * 切换后台语言
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function switch(Request $request)
    {
        $locale = $request->input('locale');
        
        // 验证语言代码是否在支持列表中
        if (!array_key_exists($locale, $this->supportedLocales)) {
            if ($request->expectsJson()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Unsupported locale: ' . $locale
                ], 400);
            }
            
            return redirect()->back()->with('error', 'Unsupported locale: ' . $locale);
        }
        
        // 将语言设置存储到session中
        session(['admin_locale' => $locale]);
        
        if ($request->expectsJson()) {
            return new JsonResponse([
                'success' => true,
                'message' => 'Language switched successfully',
                'locale' => $locale,
                'locale_name' => $this->supportedLocales[$locale]
            ]);
        }
        
        return redirect()->back()->with('success', 'Language switched to ' . $this->supportedLocales[$locale]);
    }
    
    /**
     * 获取当前后台语言
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function current()
    {
        $currentLocale = session('admin_locale', 'en');
        
        return new JsonResponse([
            'current_locale' => $currentLocale,
            'current_locale_name' => $this->supportedLocales[$currentLocale] ?? 'English',
            'supported_locales' => $this->supportedLocales
        ]);
    }
}
